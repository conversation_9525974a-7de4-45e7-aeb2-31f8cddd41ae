/* ===== Profile Page Styles ===== */

.profile-page {
    background: #f8fafc;
    font-family: var(--font-family-primary);
    padding-top: 80px;
}

/* ===== Profile Header ===== */
.profile-header {
    padding: 2rem 0;
}

.profile-banner {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    position: relative;
    overflow: hidden;
}

.profile-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.profile-banner > .row {
    position: relative;
    z-index: 2;
}

.profile-avatar {
    display: inline-block;
}

.profile-avatar img {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.profile-avatar:hover img {
    transform: scale(1.05);
}

.profile-badges .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.profile-stats .stat-item {
    padding: 0.5rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

/* ===== Profile Sidebar ===== */
.profile-sidebar {
    position: sticky;
    top: 100px;
}

.profile-sidebar .nav-pills .nav-link {
    border-radius: 10px;
    margin-bottom: 0.5rem;
    padding: 1rem;
    color: var(--dark-color);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.profile-sidebar .nav-pills .nav-link:hover {
    background-color: rgba(37, 99, 235, 0.1);
    border-color: rgba(37, 99, 235, 0.2);
    transform: translateX(-5px);
}

.profile-sidebar .nav-pills .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

/* ===== Stat Cards ===== */
.stat-card {
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
    opacity: 0;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-icon {
    opacity: 0.8;
}

/* ===== Activity Timeline ===== */
.activity-timeline {
    position: relative;
    padding-left: 2rem;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), rgba(37, 99, 235, 0.3));
}

.activity-item {
    position: relative;
    margin-bottom: 2rem;
    display: flex;
    align-items-center;
}

.activity-icon {
    position: absolute;
    left: -2rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 2;
}

.activity-content {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--primary-color);
    width: 100%;
    margin-left: 1rem;
}

/* ===== Upcoming Trip Card ===== */
.upcoming-trip {
    text-align: center;
}

.upcoming-trip img {
    transition: all 0.3s ease;
}

.upcoming-trip:hover img {
    transform: scale(1.05);
}

/* ===== Trip Items ===== */
.trip-item {
    transition: all 0.3s ease;
    border: 2px solid #e2e8f0 !important;
}

.trip-item:hover {
    border-color: var(--primary-color) !important;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
}

.trip-actions .btn {
    border-radius: 8px;
    transition: all 0.3s ease;
}

.trip-actions .btn:hover {
    transform: translateY(-2px);
}

/* ===== Cards ===== */
.card {
    border-radius: 15px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* ===== Badges ===== */
.badge {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* ===== Form Elements ===== */
.form-select {
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* ===== Buttons ===== */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.btn-outline-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tab-pane {
    animation: fadeInUp 0.5s ease-out;
}

/* ===== Loading States ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* ===== Responsive Design ===== */
@media (max-width: 992px) {
    .profile-sidebar {
        position: static;
        margin-bottom: 2rem;
    }
    
    .profile-sidebar .nav-pills {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .profile-sidebar .nav-pills .nav-link {
        flex: 1;
        text-align: center;
        margin-bottom: 0;
        padding: 0.75rem;
    }
    
    .activity-timeline {
        padding-left: 1rem;
    }
    
    .activity-timeline::before {
        left: 0.5rem;
    }
    
    .activity-icon {
        left: -1.5rem;
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
    
    .activity-content {
        margin-left: 0.5rem;
    }
}

@media (max-width: 768px) {
    .profile-banner {
        text-align: center;
    }
    
    .profile-banner .row > div {
        margin-bottom: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .trip-item .row > div {
        margin-bottom: 0.5rem;
        text-align: center;
    }
    
    .trip-actions {
        justify-content: center;
    }
}

/* ===== Custom Scrollbar ===== */
.profile-content::-webkit-scrollbar {
    width: 6px;
}

.profile-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.profile-content::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

.profile-content::-webkit-scrollbar-thumb:hover {
    background: #1d4ed8;
}

/* ===== Dark Mode Support ===== */
@media (prefers-color-scheme: dark) {
    .profile-page {
        background: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background: #2d2d2d;
        border-color: #404040;
    }
    
    .activity-content {
        background: #2d2d2d;
        color: #ffffff;
    }
}
