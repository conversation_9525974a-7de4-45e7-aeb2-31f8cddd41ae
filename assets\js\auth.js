// ===== Authentication JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    initAuthPage();
});

function initAuthPage() {
    initFormSwitching();
    initPasswordToggle();
    initPasswordStrength();
    initFormValidation();
    initSocialLogin();
    console.log('Auth page initialized');
}

// ===== Form Switching =====
function initFormSwitching() {
    const showRegisterBtn = document.getElementById('showRegister');
    const showLoginBtn = document.getElementById('showLogin');
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    
    showRegisterBtn?.addEventListener('click', function(e) {
        e.preventDefault();
        switchToRegister();
    });
    
    showLoginBtn?.addEventListener('click', function(e) {
        e.preventDefault();
        switchToLogin();
    });
}

function switchToRegister() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    
    loginForm.classList.add('d-none');
    registerForm.classList.remove('d-none');
    
    // Update welcome text
    updateWelcomeText('register');
}

function switchToLogin() {
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    
    registerForm.classList.add('d-none');
    loginForm.classList.remove('d-none');
    
    // Update welcome text
    updateWelcomeText('login');
}

function updateWelcomeText(mode) {
    const welcomeContent = document.querySelector('.welcome-content h2');
    const welcomeDesc = document.querySelector('.welcome-content .lead');
    
    if (mode === 'register') {
        welcomeContent.textContent = 'انضم إلى عائلتنا';
        welcomeDesc.textContent = 'ابدأ رحلتك معنا واكتشف عالماً من المغامرات الرائعة';
    } else {
        welcomeContent.textContent = 'مرحباً بعودتك';
        welcomeDesc.textContent = 'سجل دخولك لمتابعة رحلاتك واكتشاف وجهات جديدة';
    }
}

// ===== Password Toggle =====
function initPasswordToggle() {
    const toggleButtons = document.querySelectorAll('[id^="toggle"][id$="Password"]');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.id.replace('toggle', '').replace('Password', 'Password');
            const passwordInput = document.getElementById(targetId.charAt(0).toLowerCase() + targetId.slice(1));
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });
    });
}

// ===== Password Strength =====
function initPasswordStrength() {
    const passwordInput = document.getElementById('registerPassword');
    const strengthBar = document.getElementById('passwordStrength');
    const strengthText = document.getElementById('passwordStrengthText');
    
    if (!passwordInput || !strengthBar || !strengthText) return;
    
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        
        updatePasswordStrengthUI(strength, strengthBar, strengthText);
    });
}

function calculatePasswordStrength(password) {
    let score = 0;
    let feedback = [];
    
    // Length check
    if (password.length >= 8) score += 25;
    else feedback.push('8 أحرف على الأقل');
    
    // Uppercase check
    if (/[A-Z]/.test(password)) score += 25;
    else feedback.push('حرف كبير');
    
    // Lowercase check
    if (/[a-z]/.test(password)) score += 25;
    else feedback.push('حرف صغير');
    
    // Number or special character check
    if (/[\d\W]/.test(password)) score += 25;
    else feedback.push('رقم أو رمز');
    
    return { score, feedback };
}

function updatePasswordStrengthUI(strength, strengthBar, strengthText) {
    const { score, feedback } = strength;
    
    strengthBar.style.width = score + '%';
    strengthBar.className = 'progress-bar';
    
    if (score < 50) {
        strengthBar.classList.add('password-weak');
        strengthText.textContent = 'ضعيفة - ' + feedback.join(', ');
        strengthText.className = 'text-danger';
    } else if (score < 75) {
        strengthBar.classList.add('password-medium');
        strengthText.textContent = 'متوسطة';
        strengthText.className = 'text-warning';
    } else {
        strengthBar.classList.add('password-strong');
        strengthText.textContent = 'قوية';
        strengthText.className = 'text-success';
    }
}

// ===== Form Validation =====
function initFormValidation() {
    const loginForm = document.getElementById('loginFormSubmit');
    const registerForm = document.getElementById('registerFormSubmit');
    
    loginForm?.addEventListener('submit', handleLoginSubmit);
    registerForm?.addEventListener('submit', handleRegisterSubmit);
    
    // Real-time validation
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
}

function validateField(e) {
    const field = e.target;
    const value = field.value.trim();
    
    // Email validation
    if (field.type === 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }
    }
    
    // Password confirmation
    if (field.id === 'confirmPassword') {
        const password = document.getElementById('registerPassword').value;
        if (value !== password) {
            showFieldError(field, 'كلمات المرور غير متطابقة');
            return false;
        }
    }
    
    // Phone validation
    if (field.type === 'tel') {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(value)) {
            showFieldError(field, 'يرجى إدخال رقم هاتف صحيح');
            return false;
        }
    }
    
    clearFieldError(field);
    return true;
}

function showFieldError(field, message) {
    field.classList.add('is-invalid');
    const feedback = field.parentNode.querySelector('.invalid-feedback') || 
                    field.nextElementSibling;
    if (feedback) {
        feedback.textContent = message;
    }
}

function clearFieldError(field) {
    field.classList.remove('is-invalid');
    field.classList.add('is-valid');
}

// ===== Form Submission =====
function handleLoginSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const email = form.querySelector('#loginEmail').value;
    const password = form.querySelector('#loginPassword').value;
    const rememberMe = form.querySelector('#rememberMe').checked;
    
    if (!validateLoginForm(form)) {
        return;
    }
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    showButtonLoading(submitBtn, 'جاري تسجيل الدخول...');
    
    // Simulate API call
    setTimeout(() => {
        hideButtonLoading(submitBtn, 'تسجيل الدخول');
        
        // Simulate successful login
        showNotification('تم تسجيل الدخول بنجاح!', 'success');
        
        // Store user data (simulation)
        const userData = {
            email: email,
            name: 'أحمد محمد',
            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80'
        };
        
        localStorage.setItem('user', JSON.stringify(userData));
        if (rememberMe) {
            localStorage.setItem('rememberMe', 'true');
        }
        
        // Redirect to profile
        setTimeout(() => {
            window.location.href = 'profile.html';
        }, 1500);
        
    }, 2000);
}

function handleRegisterSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    
    if (!validateRegisterForm(form)) {
        return;
    }
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    showButtonLoading(submitBtn, 'جاري إنشاء الحساب...');
    
    // Simulate API call
    setTimeout(() => {
        hideButtonLoading(submitBtn, 'إنشاء الحساب');
        
        // Simulate successful registration
        showNotification('تم إنشاء الحساب بنجاح! مرحباً بك معنا', 'success');
        
        // Auto switch to login
        setTimeout(() => {
            switchToLogin();
            // Pre-fill email
            document.getElementById('loginEmail').value = formData.get('email');
        }, 2000);
        
    }, 2500);
}

function validateLoginForm(form) {
    const email = form.querySelector('#loginEmail');
    const password = form.querySelector('#loginPassword');
    
    let isValid = true;
    
    if (!email.value.trim()) {
        showFieldError(email, 'يرجى إدخال البريد الإلكتروني');
        isValid = false;
    }
    
    if (!password.value.trim()) {
        showFieldError(password, 'يرجى إدخال كلمة المرور');
        isValid = false;
    }
    
    return isValid;
}

function validateRegisterForm(form) {
    const inputs = form.querySelectorAll('.form-control[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateField({ target: input })) {
            isValid = false;
        }
    });
    
    return isValid;
}

// ===== Social Login =====
function initSocialLogin() {
    const socialButtons = document.querySelectorAll('.social-login .btn');
    
    socialButtons.forEach(button => {
        button.addEventListener('click', function() {
            const provider = this.querySelector('i').className.includes('google') ? 'Google' :
                           this.querySelector('i').className.includes('facebook') ? 'Facebook' : 'Twitter';
            
            showNotification(`جاري تسجيل الدخول عبر ${provider}...`, 'info');
            
            // Simulate social login
            setTimeout(() => {
                showNotification(`تم تسجيل الدخول عبر ${provider} بنجاح!`, 'success');
                setTimeout(() => {
                    window.location.href = 'profile.html';
                }, 1500);
            }, 2000);
        });
    });
}

// ===== Utility Functions =====
function showButtonLoading(button, text) {
    button.disabled = true;
    button.classList.add('btn-loading');
    button.setAttribute('data-original-text', button.innerHTML);
    button.innerHTML = text;
}

function hideButtonLoading(button, text) {
    button.disabled = false;
    button.classList.remove('btn-loading');
    button.innerHTML = button.getAttribute('data-original-text') || text;
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// ===== Forgot Password =====
document.getElementById('forgotPassword')?.addEventListener('click', function(e) {
    e.preventDefault();
    
    const email = prompt('يرجى إدخال بريدك الإلكتروني لإعادة تعيين كلمة المرور:');
    
    if (email) {
        showNotification('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'success');
    }
});
