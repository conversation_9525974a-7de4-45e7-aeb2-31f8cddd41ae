// ===== Profile Page JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    initProfilePage();
});

function initProfilePage() {
    checkUserAuthentication();
    initTabSwitching();
    initCounters();
    initProfileActions();
    loadUserData();
    console.log('Profile page initialized');
}

// ===== Authentication Check =====
function checkUserAuthentication() {
    const user = localStorage.getItem('user');
    
    if (!user) {
        // Redirect to auth page if not logged in
        window.location.href = 'auth.html';
        return;
    }
    
    // Update UI with user data
    const userData = JSON.parse(user);
    updateUserInterface(userData);
}

function updateUserInterface(userData) {
    // Update profile header
    const profileName = document.querySelector('.profile-banner h2');
    const profileEmail = document.querySelector('.profile-banner .bi-envelope').parentNode;
    const profileAvatar = document.querySelectorAll('.profile-avatar img, .navbar img');
    
    if (profileName) profileName.textContent = userData.name || 'أحمد محمد';
    if (profileEmail) profileEmail.innerHTML = `<i class="bi bi-envelope me-2"></i>${userData.email}`;
    
    profileAvatar.forEach(img => {
        if (userData.avatar) img.src = userData.avatar;
    });
}

// ===== Tab Switching =====
function initTabSwitching() {
    const tabButtons = document.querySelectorAll('#profile-tabs .nav-link');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-bs-target').replace('#', '');
            loadTabContent(targetTab);
        });
    });
}

function loadTabContent(tabName) {
    switch(tabName) {
        case 'trips':
            loadTripsData();
            break;
        case 'bookings':
            loadBookingsData();
            break;
        case 'favorites':
            loadFavoritesData();
            break;
        case 'settings':
            loadSettingsForm();
            break;
        case 'support':
            loadSupportContent();
            break;
        default:
            // Overview tab is already loaded
            break;
    }
}

// ===== Counter Animation =====
function initCounters() {
    const counters = document.querySelectorAll('.counter');
    
    const observerOptions = {
        threshold: 0.5
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    counters.forEach(counter => observer.observe(counter));
}

function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const step = target / (duration / 16); // 60fps
    let current = 0;
    
    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 16);
}

// ===== Profile Actions =====
function initProfileActions() {
    // Profile picture upload
    const avatarButton = document.querySelector('.profile-avatar .btn');
    if (avatarButton) {
        avatarButton.addEventListener('click', handleAvatarUpload);
    }
    
    // Trip actions
    document.addEventListener('click', function(e) {
        if (e.target.closest('.trip-actions .btn')) {
            handleTripAction(e.target.closest('.btn'));
        }
    });
}

function handleAvatarUpload() {
    // Create file input
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Simulate upload
            showNotification('جاري رفع الصورة...', 'info');
            
            setTimeout(() => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Update all avatar images
                    const avatars = document.querySelectorAll('.profile-avatar img, .navbar img');
                    avatars.forEach(img => img.src = e.target.result);
                    
                    // Update localStorage
                    const user = JSON.parse(localStorage.getItem('user'));
                    user.avatar = e.target.result;
                    localStorage.setItem('user', JSON.stringify(user));
                    
                    showNotification('تم تحديث الصورة الشخصية بنجاح!', 'success');
                };
                reader.readAsDataURL(file);
            }, 1500);
        }
    });
    
    fileInput.click();
}

function handleTripAction(button) {
    const action = button.querySelector('i').className.includes('eye') ? 'view' : 'download';
    const tripItem = button.closest('.trip-item');
    const tripName = tripItem.querySelector('h6').textContent;
    
    if (action === 'view') {
        showTripDetails(tripName);
    } else {
        downloadTripDocument(tripName);
    }
}

function showTripDetails(tripName) {
    const modalHTML = `
        <div class="modal fade" id="tripDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تفاصيل الرحلة - ${tripName}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <img src="https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                                     class="img-fluid rounded mb-3" alt="${tripName}">
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">معلومات الرحلة</h6>
                                <p><strong>الوجهة:</strong> ${tripName}</p>
                                <p><strong>التاريخ:</strong> 15-20 ديسمبر 2024</p>
                                <p><strong>المدة:</strong> 5 أيام</p>
                                <p><strong>عدد الأشخاص:</strong> 2</p>
                                <p><strong>الحالة:</strong> <span class="badge bg-warning">قادمة</span></p>
                                <p><strong>السعر:</strong> $899</p>
                            </div>
                        </div>
                        <hr>
                        <h6 class="fw-bold">البرنامج اليومي</h6>
                        <div class="timeline">
                            <div class="timeline-item">
                                <strong>اليوم الأول:</strong> الوصول والاستقبال
                            </div>
                            <div class="timeline-item">
                                <strong>اليوم الثاني:</strong> جولة في المدينة
                            </div>
                            <div class="timeline-item">
                                <strong>اليوم الثالث:</strong> زيارة المعالم السياحية
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary">تعديل الحجز</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal
    const existingModal = document.getElementById('tripDetailsModal');
    if (existingModal) existingModal.remove();
    
    // Add and show modal
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('tripDetailsModal'));
    modal.show();
}

function downloadTripDocument(tripName) {
    showNotification('جاري تحضير المستند...', 'info');
    
    setTimeout(() => {
        // Simulate download
        const link = document.createElement('a');
        link.href = '#'; // In real app, this would be the actual file URL
        link.download = `${tripName}-تفاصيل-الرحلة.pdf`;
        
        showNotification('تم تحميل مستند الرحلة بنجاح!', 'success');
    }, 2000);
}

// ===== Data Loading Functions =====
function loadUserData() {
    // Simulate loading user statistics
    setTimeout(() => {
        updateStatistics({
            completedTrips: 12,
            totalSpent: 8450,
            rewardPoints: 2450,
            countriesVisited: 8
        });
    }, 1000);
}

function updateStatistics(stats) {
    // Update stat cards
    const statCards = document.querySelectorAll('.stat-card h4');
    if (statCards[0]) statCards[0].textContent = stats.completedTrips;
    if (statCards[1]) statCards[1].textContent = `$${stats.totalSpent.toLocaleString()}`;
    if (statCards[2]) statCards[2].textContent = stats.rewardPoints.toLocaleString();
    if (statCards[3]) statCards[3].textContent = stats.countriesVisited;
}

function loadTripsData() {
    const tripsContainer = document.querySelector('#trips .trips-list');
    if (!tripsContainer) return;
    
    // Show loading
    tripsContainer.innerHTML = '<div class="text-center p-4"><div class="spinner-border text-primary"></div><p class="mt-2">جاري تحميل الرحلات...</p></div>';
    
    setTimeout(() => {
        const tripsHTML = generateTripsHTML();
        tripsContainer.innerHTML = tripsHTML;
    }, 1500);
}

function generateTripsHTML() {
    const trips = [
        {
            name: 'دبي - الإمارات',
            date: '15-20 ديسمبر 2024',
            people: '2 أشخاص',
            status: 'قادمة',
            statusClass: 'warning',
            image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'
        },
        {
            name: 'إسطنبول - تركيا',
            date: '10-17 نوفمبر 2024',
            people: '2 أشخاص',
            status: 'مكتملة',
            statusClass: 'success',
            image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'
        },
        {
            name: 'كوالالمبور - ماليزيا',
            date: '5-12 أكتوبر 2024',
            people: '4 أشخاص',
            status: 'مكتملة',
            statusClass: 'success',
            image: 'https://images.unsplash.com/photo-1539037116277-4db20889f2d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'
        }
    ];
    
    return trips.map(trip => `
        <div class="trip-item border rounded-3 p-3 mb-3">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <img src="${trip.image}" class="img-fluid rounded" alt="${trip.name}">
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold mb-1">${trip.name}</h6>
                    <p class="text-muted mb-1">
                        <i class="bi bi-calendar me-1"></i>
                        ${trip.date}
                    </p>
                    <p class="text-muted mb-0">
                        <i class="bi bi-people me-1"></i>
                        ${trip.people}
                    </p>
                </div>
                <div class="col-md-2 text-center">
                    <span class="badge bg-${trip.statusClass}">${trip.status}</span>
                </div>
                <div class="col-md-2 text-end">
                    <div class="trip-actions">
                        <button class="btn btn-sm btn-outline-primary me-1">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-download"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function loadBookingsData() {
    // Simulate loading bookings
    showNotification('جاري تحميل الحجوزات...', 'info');
}

function loadFavoritesData() {
    // Simulate loading favorites
    showNotification('جاري تحميل المفضلة...', 'info');
}

function loadSettingsForm() {
    // Simulate loading settings
    showNotification('جاري تحميل الإعدادات...', 'info');
}

function loadSupportContent() {
    // Simulate loading support
    showNotification('جاري تحميل صفحة الدعم...', 'info');
}

// ===== Utility Functions =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// ===== Logout Function =====
function logout() {
    localStorage.removeItem('user');
    localStorage.removeItem('rememberMe');
    showNotification('تم تسجيل الخروج بنجاح', 'success');
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Add logout event listener
document.addEventListener('click', function(e) {
    if (e.target.closest('a[href="index.html"]') && 
        e.target.closest('.dropdown-item') && 
        e.target.textContent.includes('تسجيل الخروج')) {
        e.preventDefault();
        logout();
    }
});
