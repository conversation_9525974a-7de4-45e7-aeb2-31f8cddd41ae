# Tourism Promotional Website / موقع سياحي تعريفي

A comprehensive tourism promotional website with full Arabic/RTL support built with Bootstrap 5, HTML5, CSS3, and JavaScript.

## 🌟 Features

### Core Features
- **Responsive Design**: Mobile-first approach with Bootstrap 5 grid system
- **Arabic/RTL Support**: Full right-to-left language support with language toggle
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Fast Performance**: Optimized for speed with minimal dependencies
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA labels
- **Cross-browser Compatible**: Works on all modern browsers

### Sections
1. **Hero Section**: Eye-catching banner with call-to-action buttons
2. **Features**: Key selling points with icons and descriptions
3. **Destinations**: Showcase of popular travel destinations with pricing
4. **Services**: Comprehensive list of tourism services offered
5. **Testimonials**: Customer reviews and ratings
6. **Contact Form**: Functional contact form with validation
7. **Footer**: Company information, links, and newsletter signup

### Interactive Features
- **Language Toggle**: Switch between Arabic (RTL) and English (LTR)
- **Smooth Scrolling**: Navigation with smooth scroll to sections
- **Form Validation**: Client-side validation with user feedback
- **Responsive Navigation**: Mobile-friendly collapsible menu
- **Back to Top Button**: Convenient scroll-to-top functionality
- **Hover Effects**: Interactive cards and buttons with animations

## 🛠️ Technologies Used

- **HTML5**: Semantic markup with proper structure
- **CSS3**: Modern styling with CSS Grid and Flexbox
- **Bootstrap 5**: Responsive framework for layout and components
- **JavaScript (Vanilla)**: Interactive functionality without heavy frameworks
- **Google Fonts**: Arabic web fonts (Cairo, IBM Plex Sans Arabic)
- **Bootstrap Icons**: Comprehensive icon library

## 📁 Project Structure

```
tourism-website/
├── index.html              # Main homepage with auth integration
├── destinations.html       # Professional destinations page with advanced filtering
├── offers.html            # Special offers with countdown timers
├── auth.html              # Login/Register page with social auth
├── profile.html           # User dashboard and profile management
├── assets/
│   ├── css/
│   │   ├── style.css       # Main CSS with RTL support and animations
│   │   ├── auth.css        # Authentication page styles
│   │   └── profile.css     # Profile dashboard styles
│   ├── js/
│   │   ├── main.js         # Core functionality with auth management
│   │   ├── destinations.js # Advanced filtering and search
│   │   ├── offers.js       # Countdown timers and booking modals
│   │   ├── auth.js         # Authentication and form validation
│   │   └── profile.js      # Profile management and trip tracking
│   └── images/             # Image assets (placeholder)
├── test.html              # Testing and validation page
└── README.md              # Comprehensive documentation
```

## 🚀 Getting Started

1. **Clone or Download**: Get the project files
2. **Open in Browser**: Simply open `index.html` in any modern web browser
3. **Local Server** (Optional): For development, use a local server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

## 🎨 Customization

### Colors
The website uses CSS custom properties for easy theming:
```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    /* ... more colors */
}
```

### Fonts
Arabic fonts are loaded from Google Fonts:
- **Primary**: Cairo
- **Secondary**: IBM Plex Sans Arabic

### Content
- Update text content in `index.html`
- Modify images by replacing Unsplash URLs with your own
- Customize contact information in the footer and contact section

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 992px
- **Desktop**: > 992px

## ♿ Accessibility Features

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- High contrast colors (WCAG AA compliant)
- Alternative text for images
- Screen reader compatibility
- Focus indicators for interactive elements

## 🌐 Browser Support

- Chrome (latest 2 versions)
- Firefox (latest 2 versions)
- Safari (latest 2 versions)
- Edge (latest 2 versions)

## 📋 Form Functionality

**Note**: This is a frontend-only implementation. Forms use JavaScript simulation:
- Contact form displays success/error messages
- Newsletter signup shows confirmation
- No actual backend processing (ready for integration)

## 🔧 Development Notes

### Adding New Destinations
1. Add new card in the destinations section
2. Update the destination dropdown in contact form
3. Add to footer popular destinations list

### Language Support
The website includes a language toggle that switches:
- Text direction (RTL/LTR)
- Content language
- Navigation items
- Form labels

### Performance Optimization
- Optimized images (use WebP format when possible)
- Minified CSS and JS for production
- Lazy loading for images
- Efficient CSS selectors

## 📞 Contact Information

For questions or support regarding this website template:
- Email: <EMAIL>
- Phone: +966 12 345 6789

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

---

## 🎉 Recent Updates

### Version 2.0 Features:
- ✅ **Enhanced Hero Section** with background image and statistics
- ✅ **Improved Card Design** with uniform dimensions and better hover effects
- ✅ **New Destinations Page** with filtering and search functionality
- ✅ **Special Offers Page** with countdown timers and flash deals
- ✅ **Better Navigation** with offers section and improved mobile menu
- ✅ **Enhanced Animations** with fade-in effects and smooth transitions
- ✅ **Copyright Updated** to Mohamed Hashish
- ✅ **Removed Book Now Button** from header for cleaner design

### Version 3.0 Features (Professional Upgrade):
- ✅ **Advanced Destinations Page** with professional hero section and comprehensive filtering
- ✅ **User Authentication System** with login/register functionality
- ✅ **Personal Profile Dashboard** with trip management and user statistics
- ✅ **Professional Filter System** with price range, duration, activity type, and more
- ✅ **Enhanced Search Functionality** with real-time filtering and sorting
- ✅ **User Session Management** with persistent login and user data
- ✅ **Interactive Counters** and animated statistics
- ✅ **Professional UI/UX** with modern design patterns and smooth animations

### Technical Improvements:
- Enhanced CSS with better responsive design
- Improved JavaScript functionality for all pages
- Better accessibility and performance optimization
- Updated project structure with multiple pages

---

**Built with ❤️ for the tourism industry by Mohamed Hashish**
