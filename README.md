# Tourism Promotional Website / موقع سياحي تعريفي

A comprehensive tourism promotional website with full Arabic/RTL support built with Bootstrap 5, HTML5, CSS3, and JavaScript.

## 🌟 Features

### Core Features
- **Responsive Design**: Mobile-first approach with Bootstrap 5 grid system
- **Arabic/RTL Support**: Full right-to-left language support with language toggle
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Fast Performance**: Optimized for speed with minimal dependencies
- **Accessibility**: WCAG 2.1 AA compliant with proper ARIA labels
- **Cross-browser Compatible**: Works on all modern browsers

### Sections
1. **Hero Section**: Eye-catching banner with call-to-action buttons
2. **Features**: Key selling points with icons and descriptions
3. **Destinations**: Showcase of popular travel destinations with pricing
4. **Services**: Comprehensive list of tourism services offered
5. **Testimonials**: Customer reviews and ratings
6. **Contact Form**: Functional contact form with validation
7. **Footer**: Company information, links, and newsletter signup

### Interactive Features
- **Language Toggle**: Switch between Arabic (RTL) and English (LTR)
- **Smooth Scrolling**: Navigation with smooth scroll to sections
- **Form Validation**: Client-side validation with user feedback
- **Responsive Navigation**: Mobile-friendly collapsible menu
- **Back to Top Button**: Convenient scroll-to-top functionality
- **Hover Effects**: Interactive cards and buttons with animations

## 🛠️ Technologies Used

- **HTML5**: Semantic markup with proper structure
- **CSS3**: Modern styling with CSS Grid and Flexbox
- **Bootstrap 5**: Responsive framework for layout and components
- **JavaScript (Vanilla)**: Interactive functionality without heavy frameworks
- **Google Fonts**: Arabic web fonts (Cairo, IBM Plex Sans Arabic)
- **Bootstrap Icons**: Comprehensive icon library

## 📁 Project Structure

```
tourism-website/
├── index.html              # Main HTML file
├── assets/
│   ├── css/
│   │   └── style.css       # Custom CSS styles
│   ├── js/
│   │   └── main.js         # JavaScript functionality
│   └── images/             # Image assets (placeholder)
└── README.md               # Project documentation
```

## 🚀 Getting Started

1. **Clone or Download**: Get the project files
2. **Open in Browser**: Simply open `index.html` in any modern web browser
3. **Local Server** (Optional): For development, use a local server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

## 🎨 Customization

### Colors
The website uses CSS custom properties for easy theming:
```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    /* ... more colors */
}
```

### Fonts
Arabic fonts are loaded from Google Fonts:
- **Primary**: Cairo
- **Secondary**: IBM Plex Sans Arabic

### Content
- Update text content in `index.html`
- Modify images by replacing Unsplash URLs with your own
- Customize contact information in the footer and contact section

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 992px
- **Desktop**: > 992px

## ♿ Accessibility Features

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- High contrast colors (WCAG AA compliant)
- Alternative text for images
- Screen reader compatibility
- Focus indicators for interactive elements

## 🌐 Browser Support

- Chrome (latest 2 versions)
- Firefox (latest 2 versions)
- Safari (latest 2 versions)
- Edge (latest 2 versions)

## 📋 Form Functionality

**Note**: This is a frontend-only implementation. Forms use JavaScript simulation:
- Contact form displays success/error messages
- Newsletter signup shows confirmation
- No actual backend processing (ready for integration)

## 🔧 Development Notes

### Adding New Destinations
1. Add new card in the destinations section
2. Update the destination dropdown in contact form
3. Add to footer popular destinations list

### Language Support
The website includes a language toggle that switches:
- Text direction (RTL/LTR)
- Content language
- Navigation items
- Form labels

### Performance Optimization
- Optimized images (use WebP format when possible)
- Minified CSS and JS for production
- Lazy loading for images
- Efficient CSS selectors

## 📞 Contact Information

For questions or support regarding this website template:
- Email: <EMAIL>
- Phone: +966 12 345 6789

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

---

**Built with ❤️ for the tourism industry**
