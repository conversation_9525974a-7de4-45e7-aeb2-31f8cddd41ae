// ===== Tourism Website JavaScript =====

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

// ===== Main Initialization Function =====
function initializeWebsite() {
    initNavbar();
    initLanguageToggle();
    initSmoothScrolling();
    initAnimations();
    initFormHandling();
    
    console.log('Tourism website initialized successfully');
}

// ===== Navigation Functions =====
function initNavbar() {
    const navbar = document.getElementById('mainNavbar');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // Active link highlighting
    window.addEventListener('scroll', function() {
        let current = '';
        const sections = document.querySelectorAll('section[id]');
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (window.scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    });
    
    // Mobile menu close on link click
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            const navbarCollapse = document.getElementById('navbarNav');
            if (navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        });
    });
}

// ===== Language Toggle =====
function initLanguageToggle() {
    const languageToggle = document.getElementById('languageToggle');
    const html = document.documentElement;
    
    languageToggle.addEventListener('click', function() {
        const currentLang = html.getAttribute('lang');
        const currentDir = html.getAttribute('dir');
        
        if (currentLang === 'ar') {
            // Switch to English
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            languageToggle.innerHTML = '<i class="bi bi-translate"></i> <span class="d-none d-sm-inline ms-1">العربية</span>';
            
            // Update content to English (simplified for demo)
            updateContentToEnglish();
        } else {
            // Switch to Arabic
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            languageToggle.innerHTML = '<i class="bi bi-translate"></i> <span class="d-none d-sm-inline ms-1">English</span>';
            
            // Update content to Arabic
            updateContentToArabic();
        }
        
        // Store language preference
        localStorage.setItem('preferredLanguage', html.getAttribute('lang'));
        localStorage.setItem('preferredDirection', html.getAttribute('dir'));
    });
    
    // Load saved language preference
    const savedLang = localStorage.getItem('preferredLanguage');
    const savedDir = localStorage.getItem('preferredDirection');
    
    if (savedLang && savedDir) {
        html.setAttribute('lang', savedLang);
        html.setAttribute('dir', savedDir);
        
        if (savedLang === 'en') {
            languageToggle.innerHTML = '<i class="bi bi-translate"></i> <span class="d-none d-sm-inline ms-1">العربية</span>';
            updateContentToEnglish();
        }
    }
}

// ===== Content Translation Functions =====
function updateContentToEnglish() {
    // Update navigation
    document.querySelector('.navbar-brand').innerHTML = '<i class="bi bi-airplane-engines me-2"></i>Travel Tours';
    
    const navItems = document.querySelectorAll('.nav-link');
    const englishNavTexts = ['Home', 'Destinations', 'Services', 'Testimonials', 'Contact', 'Offers'];
    navItems.forEach((item, index) => {
        if (index < englishNavTexts.length) {
            item.textContent = englishNavTexts[index];
        }
    });
    
    // Update hero section
    document.querySelector('.hero-section h1').textContent = 'Discover the World with Us';
    document.querySelector('.hero-section .lead').textContent = 'Exceptional travel experiences to the most beautiful destinations around the world. We offer unforgettable experiences with the best services and affordable prices.';
    
    // Update buttons
    const heroButtons = document.querySelectorAll('.hero-buttons .btn');
    if (heroButtons[0]) heroButtons[0].innerHTML = '<i class="bi bi-compass me-2"></i>Explore Destinations';
    if (heroButtons[1]) heroButtons[1].innerHTML = '<i class="bi bi-telephone me-2"></i>Book Now';
    
    // Update features
    const featureHeadings = document.querySelectorAll('.feature-icon + h5');
    const featureTexts = document.querySelectorAll('.feature-icon + h5 + p');
    
    const englishFeatures = [
        { title: 'Safety & Trust', text: 'Safe and guaranteed trips with the best airlines and hotels' },
        { title: 'Affordable Prices', text: 'Competitive offers and prices with installment payment options' },
        { title: '24/7 Support', text: 'Support team available around the clock to help you' },
        { title: 'Premium Experiences', text: 'Tourism programs specially designed to suit your needs' }
    ];
    
    englishFeatures.forEach((feature, index) => {
        if (featureHeadings[index]) featureHeadings[index].textContent = feature.title;
        if (featureTexts[index]) featureTexts[index].textContent = feature.text;
    });
}

function updateContentToArabic() {
    // Update navigation
    document.querySelector('.navbar-brand').innerHTML = '<i class="bi bi-airplane-engines me-2"></i>الرحلات السياحية';
    
    const navItems = document.querySelectorAll('.nav-link');
    const arabicNavTexts = ['الرئيسية', 'الوجهات', 'الخدمات', 'آراء العملاء', 'اتصل بنا', 'العروض'];
    navItems.forEach((item, index) => {
        if (index < arabicNavTexts.length) {
            item.textContent = arabicNavTexts[index];
        }
    });
    
    // Update hero section
    document.querySelector('.hero-section h1').textContent = 'اكتشف العالم معنا';
    document.querySelector('.hero-section .lead').textContent = 'رحلات سياحية استثنائية إلى أجمل الوجهات حول العالم. نحن نقدم تجارب لا تُنسى مع أفضل الخدمات والأسعار المناسبة';
    
    // Update buttons
    const heroButtons = document.querySelectorAll('.hero-buttons .btn');
    if (heroButtons[0]) heroButtons[0].innerHTML = '<i class="bi bi-compass me-2"></i>استكشف الوجهات';
    if (heroButtons[1]) heroButtons[1].innerHTML = '<i class="bi bi-telephone me-2"></i>احجز الآن';
    
    // Update features
    const featureHeadings = document.querySelectorAll('.feature-icon + h5');
    const featureTexts = document.querySelectorAll('.feature-icon + h5 + p');
    
    const arabicFeatures = [
        { title: 'أمان وثقة', text: 'رحلات آمنة ومضمونة مع أفضل شركات الطيران والفنادق' },
        { title: 'أسعار مناسبة', text: 'عروض وأسعار تنافسية مع إمكانية الدفع بالتقسيط' },
        { title: 'دعم 24/7', text: 'فريق دعم متاح على مدار الساعة لمساعدتك' },
        { title: 'تجارب مميزة', text: 'برامج سياحية مصممة خصيصاً لتناسب احتياجاتك' }
    ];
    
    arabicFeatures.forEach((feature, index) => {
        if (featureHeadings[index]) featureHeadings[index].textContent = feature.title;
        if (featureTexts[index]) featureTexts[index].textContent = feature.text;
    });
}

// ===== Smooth Scrolling =====
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== Animation on Scroll =====
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    }, observerOptions);
    
    // Observe elements that should animate
    const animateElements = document.querySelectorAll('.feature-icon, .card, .testimonial-item');
    animateElements.forEach(el => observer.observe(el));
}

// ===== Form Handling =====
function initFormHandling() {
    // Contact Form
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }

    // Newsletter Form
    const newsletterForm = document.getElementById('newsletterForm');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', handleNewsletterForm);
    }

    // Back to Top Button
    initBackToTop();

    console.log('Form handling initialized');
}

// ===== Contact Form Handler =====
function handleContactForm(e) {
    e.preventDefault();

    const form = e.target;
    const formData = new FormData(form);

    // Validate form
    if (!form.checkValidity()) {
        e.stopPropagation();
        form.classList.add('was-validated');
        return;
    }

    // Simulate form submission
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // Show loading state
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإرسال...';
    submitBtn.disabled = true;

    // Simulate API call
    setTimeout(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // Show success message
        showNotification('تم إرسال طلبك بنجاح! سنتواصل معك قريباً.', 'success');

        // Reset form
        form.reset();
        form.classList.remove('was-validated');

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });

    }, 2000);
}

// ===== Newsletter Form Handler =====
function handleNewsletterForm(e) {
    e.preventDefault();

    const form = e.target;
    const emailInput = form.querySelector('input[type="email"]');
    const submitBtn = form.querySelector('button[type="submit"]');

    if (!emailInput.value || !emailInput.checkValidity()) {
        emailInput.classList.add('is-invalid');
        showNotification('يرجى إدخال بريد إلكتروني صحيح', 'danger');
        return;
    }

    // Show loading state
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
    submitBtn.disabled = true;

    // Simulate subscription
    setTimeout(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // Show success message
        showNotification('تم الاشتراك في النشرة الإخبارية بنجاح!', 'success');

        // Reset form
        emailInput.value = '';
        emailInput.classList.remove('is-invalid');

    }, 1500);
}

// ===== Back to Top Button =====
function initBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');

    if (!backToTopBtn) return;

    // Show/hide button based on scroll position
    window.addEventListener('scroll', debounce(() => {
        if (window.scrollY > 300) {
            backToTopBtn.style.display = 'block';
            setTimeout(() => {
                backToTopBtn.style.opacity = '1';
                backToTopBtn.style.transform = 'scale(1)';
            }, 10);
        } else {
            backToTopBtn.style.opacity = '0';
            backToTopBtn.style.transform = 'scale(0.8)';
            setTimeout(() => {
                if (window.scrollY <= 300) {
                    backToTopBtn.style.display = 'none';
                }
            }, 300);
        }
    }, 100));

    // Click handler
    backToTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Initial styles
    backToTopBtn.style.opacity = '0';
    backToTopBtn.style.transform = 'scale(0.8)';
    backToTopBtn.style.transition = 'all 0.3s ease';
}

// ===== Utility Functions =====
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// ===== Error Handling =====
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
});

// ===== Performance Optimization =====
// Debounce function for scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
