// ===== Destinations Page JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    initDestinationsPage();
});

function initDestinationsPage() {
    initFilterButtons();
    initDestinationCards();
    console.log('Destinations page initialized');
}

// ===== Filter Functionality =====
function initFilterButtons() {
    const filterButtons = document.querySelectorAll('[data-filter]');
    const destinationItems = document.querySelectorAll('.destination-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter destinations
            destinationItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    item.classList.add('animate-fade-in');
                } else {
                    item.style.display = 'none';
                    item.classList.remove('animate-fade-in');
                }
            });
            
            // Show notification
            const filterText = this.textContent;
            showNotification(`تم تطبيق فلتر: ${filterText}`, 'info');
        });
    });
}

// ===== Destination Cards Enhancement =====
function initDestinationCards() {
    const cards = document.querySelectorAll('.destination-card');
    
    cards.forEach(card => {
        // Add hover effect for booking button
        const bookBtn = card.querySelector('.btn-primary');
        if (bookBtn) {
            bookBtn.addEventListener('mouseenter', function() {
                this.innerHTML = '<i class="bi bi-heart-fill me-2"></i>أضف للمفضلة';
            });
            
            bookBtn.addEventListener('mouseleave', function() {
                this.innerHTML = 'احجز الآن';
            });
        }
        
        // Add click tracking
        card.addEventListener('click', function(e) {
            if (!e.target.closest('.btn')) {
                const title = this.querySelector('.card-title').textContent;
                console.log(`Destination clicked: ${title}`);
                
                // Could track analytics here
                trackDestinationView(title);
            }
        });
    });
}

// ===== Analytics Tracking =====
function trackDestinationView(destinationName) {
    // Simulate analytics tracking
    console.log(`Analytics: Destination viewed - ${destinationName}`);
    
    // In a real application, you would send this to your analytics service
    // Example: gtag('event', 'destination_view', { destination: destinationName });
}

// ===== Search Functionality (if needed) =====
function initDestinationSearch() {
    const searchInput = document.getElementById('destinationSearch');
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const destinationItems = document.querySelectorAll('.destination-item');
        
        destinationItems.forEach(item => {
            const title = item.querySelector('.card-title').textContent.toLowerCase();
            const description = item.querySelector('.card-text').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
}

// ===== Load More Destinations =====
function loadMoreDestinations() {
    // Simulate loading more destinations
    const grid = document.getElementById('destinationsGrid');
    const loadingHTML = `
        <div class="col-12 text-center" id="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل المزيد من الوجهات...</p>
        </div>
    `;
    
    grid.insertAdjacentHTML('beforeend', loadingHTML);
    
    // Simulate API call delay
    setTimeout(() => {
        document.getElementById('loading').remove();
        
        // Add new destinations (example)
        const newDestinations = [
            {
                name: 'اليابان - طوكيو',
                image: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                price: '$1299',
                category: 'asia',
                rating: 5,
                days: 8,
                people: '2-4'
            }
            // Add more destinations as needed
        ];
        
        newDestinations.forEach(dest => {
            const destHTML = createDestinationCard(dest);
            grid.insertAdjacentHTML('beforeend', destHTML);
        });
        
        showNotification('تم تحميل وجهات جديدة!', 'success');
    }, 2000);
}

// ===== Create Destination Card =====
function createDestinationCard(destination) {
    const stars = '★'.repeat(destination.rating) + '☆'.repeat(5 - destination.rating);
    
    return `
        <div class="col-lg-4 col-md-6 destination-item animate-fade-in" data-category="${destination.category}">
            <div class="card destination-card h-100 border-0 shadow-sm">
                <div class="card-img-wrapper position-relative overflow-hidden">
                    <img src="${destination.image}" class="card-img-top" alt="${destination.name}">
                    <div class="card-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-end">
                        <div class="p-3 text-white">
                            <span class="badge bg-success mb-2">جديد</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title fw-bold">${destination.name}</h5>
                    <p class="card-text text-muted">وجهة سياحية رائعة تستحق الزيارة</p>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="h5 text-primary fw-bold">${destination.price}</span>
                            <small class="text-muted">/شخص</small>
                        </div>
                        <div class="text-warning">
                            ${stars}
                        </div>
                    </div>
                    <div class="destination-features mb-3">
                        <small class="text-muted">
                            <i class="bi bi-calendar me-1"></i> ${destination.days} أيام
                            <i class="bi bi-people me-1 ms-3"></i> ${destination.people} أشخاص
                        </small>
                    </div>
                    <a href="index.html#contact" class="btn btn-primary w-100">احجز الآن</a>
                </div>
            </div>
        </div>
    `;
}

// ===== Utility Functions =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 4000);
}
