// ===== Destinations Page JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    initDestinationsPage();
});

function initDestinationsPage() {
    initHeroSearch();
    initAdvancedFilters();
    initFilterButtons();
    initDestinationCards();
    initViewToggle();
    initCounters();
    console.log('Destinations page initialized');
}

// ===== Filter Functionality =====
function initFilterButtons() {
    const filterButtons = document.querySelectorAll('[data-filter]');
    const destinationItems = document.querySelectorAll('.destination-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter destinations
            destinationItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    item.classList.add('animate-fade-in');
                } else {
                    item.style.display = 'none';
                    item.classList.remove('animate-fade-in');
                }
            });
            
            // Show notification
            const filterText = this.textContent;
            showNotification(`تم تطبيق فلتر: ${filterText}`, 'info');
        });
    });
}

// ===== Destination Cards Enhancement =====
function initDestinationCards() {
    const cards = document.querySelectorAll('.destination-card');
    
    cards.forEach(card => {
        // Add hover effect for booking button
        const bookBtn = card.querySelector('.btn-primary');
        if (bookBtn) {
            bookBtn.addEventListener('mouseenter', function() {
                this.innerHTML = '<i class="bi bi-heart-fill me-2"></i>أضف للمفضلة';
            });
            
            bookBtn.addEventListener('mouseleave', function() {
                this.innerHTML = 'احجز الآن';
            });
        }
        
        // Add click tracking
        card.addEventListener('click', function(e) {
            if (!e.target.closest('.btn')) {
                const title = this.querySelector('.card-title').textContent;
                console.log(`Destination clicked: ${title}`);
                
                // Could track analytics here
                trackDestinationView(title);
            }
        });
    });
}

// ===== Analytics Tracking =====
function trackDestinationView(destinationName) {
    // Simulate analytics tracking
    console.log(`Analytics: Destination viewed - ${destinationName}`);
    
    // In a real application, you would send this to your analytics service
    // Example: gtag('event', 'destination_view', { destination: destinationName });
}

// ===== Search Functionality (if needed) =====
function initDestinationSearch() {
    const searchInput = document.getElementById('destinationSearch');
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const destinationItems = document.querySelectorAll('.destination-item');
        
        destinationItems.forEach(item => {
            const title = item.querySelector('.card-title').textContent.toLowerCase();
            const description = item.querySelector('.card-text').textContent.toLowerCase();
            
            if (title.includes(searchTerm) || description.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
}

// ===== Load More Destinations =====
function loadMoreDestinations() {
    // Simulate loading more destinations
    const grid = document.getElementById('destinationsGrid');
    const loadingHTML = `
        <div class="col-12 text-center" id="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2">جاري تحميل المزيد من الوجهات...</p>
        </div>
    `;
    
    grid.insertAdjacentHTML('beforeend', loadingHTML);
    
    // Simulate API call delay
    setTimeout(() => {
        document.getElementById('loading').remove();
        
        // Add new destinations (example)
        const newDestinations = [
            {
                name: 'اليابان - طوكيو',
                image: 'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
                price: '$1299',
                category: 'asia',
                rating: 5,
                days: 8,
                people: '2-4'
            }
            // Add more destinations as needed
        ];
        
        newDestinations.forEach(dest => {
            const destHTML = createDestinationCard(dest);
            grid.insertAdjacentHTML('beforeend', destHTML);
        });
        
        showNotification('تم تحميل وجهات جديدة!', 'success');
    }, 2000);
}

// ===== Create Destination Card =====
function createDestinationCard(destination) {
    const stars = '★'.repeat(destination.rating) + '☆'.repeat(5 - destination.rating);
    
    return `
        <div class="col-lg-4 col-md-6 destination-item animate-fade-in" data-category="${destination.category}">
            <div class="card destination-card h-100 border-0 shadow-sm">
                <div class="card-img-wrapper position-relative overflow-hidden">
                    <img src="${destination.image}" class="card-img-top" alt="${destination.name}">
                    <div class="card-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-end">
                        <div class="p-3 text-white">
                            <span class="badge bg-success mb-2">جديد</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title fw-bold">${destination.name}</h5>
                    <p class="card-text text-muted">وجهة سياحية رائعة تستحق الزيارة</p>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="h5 text-primary fw-bold">${destination.price}</span>
                            <small class="text-muted">/شخص</small>
                        </div>
                        <div class="text-warning">
                            ${stars}
                        </div>
                    </div>
                    <div class="destination-features mb-3">
                        <small class="text-muted">
                            <i class="bi bi-calendar me-1"></i> ${destination.days} أيام
                            <i class="bi bi-people me-1 ms-3"></i> ${destination.people} أشخاص
                        </small>
                    </div>
                    <a href="index.html#contact" class="btn btn-primary w-100">احجز الآن</a>
                </div>
            </div>
        </div>
    `;
}

// ===== Hero Search =====
function initHeroSearch() {
    const quickSearch = document.getElementById('quickSearch');
    const budgetFilter = document.getElementById('budgetFilter');
    const durationFilter = document.getElementById('durationFilter');
    const searchBtn = document.getElementById('searchBtn');

    searchBtn?.addEventListener('click', performQuickSearch);
    quickSearch?.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') performQuickSearch();
    });
}

function performQuickSearch() {
    const searchTerm = document.getElementById('quickSearch').value;
    const budget = document.getElementById('budgetFilter').value;
    const duration = document.getElementById('durationFilter').value;

    // Apply filters
    applyAdvancedFilters({
        search: searchTerm,
        budget: budget,
        duration: duration
    });

    // Scroll to results
    document.getElementById('advanced-filters').scrollIntoView({ behavior: 'smooth' });
}

// ===== Advanced Filters =====
function initAdvancedFilters() {
    const priceRange = document.getElementById('priceRange');
    const priceValue = document.getElementById('priceValue');
    const resetBtn = document.getElementById('resetFilters');
    const saveBtn = document.getElementById('saveFilters');

    // Price range slider
    priceRange?.addEventListener('input', function() {
        priceValue.textContent = this.value;
        applyFilters();
    });

    // Reset filters
    resetBtn?.addEventListener('click', resetAllFilters);

    // Save filters
    saveBtn?.addEventListener('click', saveCurrentFilters);

    // Filter change listeners
    const filterElements = document.querySelectorAll('#advanced-filters select, #advanced-filters input[type="checkbox"], #advanced-filters input[type="radio"]');
    filterElements.forEach(element => {
        element.addEventListener('change', applyFilters);
    });

    // Region filter special handling
    const regionAll = document.getElementById('region-all');
    const regionFilters = document.querySelectorAll('.region-filter');

    regionAll?.addEventListener('change', function() {
        if (this.checked) {
            regionFilters.forEach(filter => filter.checked = false);
        }
        applyFilters();
    });

    regionFilters.forEach(filter => {
        filter.addEventListener('change', function() {
            if (this.checked) {
                regionAll.checked = false;
            }
            applyFilters();
        });
    });
}

function applyFilters() {
    const filters = getCurrentFilters();
    applyAdvancedFilters(filters);
}

function getCurrentFilters() {
    return {
        regions: getSelectedRegions(),
        maxPrice: document.getElementById('priceRange')?.value || 2000,
        duration: document.getElementById('durationSelect')?.value || '',
        activity: document.getElementById('activitySelect')?.value || '',
        rating: document.querySelector('input[name="rating"]:checked')?.value || '',
        groupSize: document.getElementById('groupSizeSelect')?.value || '',
        season: document.getElementById('seasonSelect')?.value || '',
        sort: document.getElementById('sortSelect')?.value || 'popular'
    };
}

function getSelectedRegions() {
    const regionAll = document.getElementById('region-all');
    if (regionAll?.checked) return ['all'];

    const selectedRegions = [];
    document.querySelectorAll('.region-filter:checked').forEach(filter => {
        selectedRegions.push(filter.value);
    });

    return selectedRegions.length > 0 ? selectedRegions : ['all'];
}

function applyAdvancedFilters(filters) {
    const destinations = document.querySelectorAll('.destination-item');
    let visibleCount = 0;

    destinations.forEach(destination => {
        if (matchesFilters(destination, filters)) {
            destination.style.display = 'block';
            destination.classList.add('animate-fade-in');
            visibleCount++;
        } else {
            destination.style.display = 'none';
            destination.classList.remove('animate-fade-in');
        }
    });

    updateResultsCount(visibleCount);

    if (visibleCount === 0) {
        showNoResultsMessage();
    } else {
        hideNoResultsMessage();
    }
}

function matchesFilters(destination, filters) {
    // Region filter
    const category = destination.getAttribute('data-category');
    if (filters.regions && !filters.regions.includes('all') && !filters.regions.includes(category)) {
        return false;
    }

    // Price filter
    const priceElement = destination.querySelector('.new-price, .h5');
    if (priceElement) {
        const price = parseInt(priceElement.textContent.replace(/[^0-9]/g, ''));
        if (price > filters.maxPrice) return false;
    }

    // Search term
    if (filters.search) {
        const title = destination.querySelector('.card-title').textContent.toLowerCase();
        const description = destination.querySelector('.card-text').textContent.toLowerCase();
        const searchTerm = filters.search.toLowerCase();

        if (!title.includes(searchTerm) && !description.includes(searchTerm)) {
            return false;
        }
    }

    // Duration filter
    if (filters.duration) {
        const durationElement = destination.querySelector('.destination-features');
        if (durationElement) {
            const durationText = durationElement.textContent;
            const days = parseInt(durationText.match(/\d+/)?.[0] || 0);

            if (!matchesDurationFilter(days, filters.duration)) {
                return false;
            }
        }
    }

    return true;
}

function matchesDurationFilter(days, durationFilter) {
    switch(durationFilter) {
        case '1-3': return days >= 1 && days <= 3;
        case '4-7': return days >= 4 && days <= 7;
        case '8-14': return days >= 8 && days <= 14;
        case '15+': return days >= 15;
        default: return true;
    }
}

function resetAllFilters() {
    // Reset all form elements
    document.getElementById('priceRange').value = 2000;
    document.getElementById('priceValue').textContent = '2000';
    document.getElementById('durationSelect').value = '';
    document.getElementById('activitySelect').value = '';
    document.getElementById('groupSizeSelect').value = '';
    document.getElementById('seasonSelect').value = '';
    document.getElementById('sortSelect').value = 'popular';

    // Reset region filters
    document.getElementById('region-all').checked = true;
    document.querySelectorAll('.region-filter').forEach(filter => filter.checked = false);

    // Reset rating
    document.getElementById('rating-all').checked = true;

    // Reset hero search
    document.getElementById('quickSearch').value = '';
    document.getElementById('budgetFilter').value = '';
    document.getElementById('durationFilter').value = '';

    // Show all destinations
    document.querySelectorAll('.destination-item').forEach(item => {
        item.style.display = 'block';
    });

    updateResultsCount(document.querySelectorAll('.destination-item').length);
    showNotification('تم إعادة تعيين جميع الفلاتر', 'success');
}

function saveCurrentFilters() {
    const filters = getCurrentFilters();
    localStorage.setItem('savedDestinationFilters', JSON.stringify(filters));
    showNotification('تم حفظ إعدادات البحث بنجاح!', 'success');
}

// ===== View Toggle =====
function initViewToggle() {
    const gridView = document.getElementById('gridView');
    const listView = document.getElementById('listView');
    const destinationsGrid = document.getElementById('destinationsGrid');

    gridView?.addEventListener('click', function() {
        destinationsGrid.className = 'row g-4';
        this.classList.add('active');
        listView.classList.remove('active');
    });

    listView?.addEventListener('click', function() {
        destinationsGrid.className = 'row g-2';
        this.classList.add('active');
        gridView.classList.remove('active');

        // Modify cards for list view
        const cards = destinationsGrid.querySelectorAll('.destination-card');
        cards.forEach(card => {
            card.style.flexDirection = 'row';
            card.style.maxHeight = '200px';
        });
    });
}

// ===== Counter Animation =====
function initCounters() {
    const counters = document.querySelectorAll('.counter');

    const observerOptions = {
        threshold: 0.5
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    counters.forEach(counter => observer.observe(counter));
}

function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-target'));
    const duration = 2000;
    const step = target / (duration / 16);
    let current = 0;

    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 16);
}

// ===== Results Management =====
function updateResultsCount(count) {
    const resultsCount = document.getElementById('resultsCount');
    if (resultsCount) {
        resultsCount.textContent = count;
    }
}

function showNoResultsMessage() {
    const grid = document.getElementById('destinationsGrid');
    const existingMessage = document.getElementById('noResultsMessage');

    if (!existingMessage) {
        const messageHTML = `
            <div id="noResultsMessage" class="col-12 text-center py-5">
                <div class="no-results">
                    <i class="bi bi-search display-1 text-muted mb-3"></i>
                    <h4 class="text-muted">لم يتم العثور على نتائج</h4>
                    <p class="text-muted">جرب تعديل معايير البحث أو إعادة تعيين الفلاتر</p>
                    <button class="btn btn-primary" onclick="resetAllFilters()">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        إعادة تعيين الفلاتر
                    </button>
                </div>
            </div>
        `;
        grid.insertAdjacentHTML('beforeend', messageHTML);
    }
}

function hideNoResultsMessage() {
    const message = document.getElementById('noResultsMessage');
    if (message) {
        message.remove();
    }
}

// ===== Utility Functions =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 4000);
}
