// ===== Offers Page JavaScript =====

document.addEventListener('DOMContentLoaded', function() {
    initOffersPage();
});

function initOffersPage() {
    initCountdownTimers();
    initOfferCards();
    initFlashDeals();
    console.log('Offers page initialized');
}

// ===== Countdown Timer Functionality =====
function initCountdownTimers() {
    const timers = document.querySelectorAll('.timer-display');
    
    timers.forEach(timer => {
        const endDate = timer.getAttribute('data-countdown');
        if (endDate) {
            startCountdown(timer, endDate);
        }
    });
}

function startCountdown(timerElement, endDate) {
    const countdownDate = new Date(endDate).getTime();
    
    const updateTimer = () => {
        const now = new Date().getTime();
        const distance = countdownDate - now;
        
        if (distance < 0) {
            timerElement.innerHTML = "انتهى العرض";
            timerElement.parentElement.classList.add('expired');
            return;
        }
        
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        timerElement.querySelector('.days').textContent = days.toString().padStart(2, '0');
        timerElement.querySelector('.hours').textContent = hours.toString().padStart(2, '0');
        timerElement.querySelector('.minutes').textContent = minutes.toString().padStart(2, '0');
        
        // Add urgency styling when time is running out
        if (days < 1) {
            timerElement.parentElement.classList.add('urgent');
        }
    };
    
    // Update immediately and then every second
    updateTimer();
    setInterval(updateTimer, 1000);
}

// ===== Flash Deals Enhancement =====
function initFlashDeals() {
    const flashDeals = document.querySelectorAll('.flash-deal');
    
    flashDeals.forEach(deal => {
        // Add pulsing effect to urgent deals
        const timer = deal.querySelector('.countdown-timer');
        if (timer) {
            deal.classList.add('pulse-deal');
        }
        
        // Track deal clicks
        deal.addEventListener('click', function(e) {
            if (e.target.closest('.btn')) {
                const dealTitle = this.querySelector('h3').textContent;
                trackDealClick(dealTitle);
                
                // Show booking urgency message
                showUrgencyMessage();
            }
        });
    });
}

// ===== Offer Cards Enhancement =====
function initOfferCards() {
    const offerCards = document.querySelectorAll('.offer-card');
    
    offerCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
        
        // Add click tracking
        const bookBtn = card.querySelector('.btn');
        if (bookBtn) {
            bookBtn.addEventListener('click', function(e) {
                e.preventDefault();
                
                const offerTitle = card.querySelector('.card-title, h3').textContent;
                const price = card.querySelector('.new-price').textContent;
                
                // Show booking confirmation
                showBookingModal(offerTitle, price);
                
                // Track offer booking attempt
                trackOfferBooking(offerTitle, price);
            });
        }
    });
}

// ===== Booking Modal =====
function showBookingModal(offerTitle, price) {
    const modalHTML = `
        <div class="modal fade" id="bookingModal" tabindex="-1">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="bi bi-calendar-check me-2"></i>
                            تأكيد الحجز
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div class="mb-4">
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                        </div>
                        <h4 class="fw-bold mb-3">${offerTitle}</h4>
                        <p class="lead text-primary fw-bold mb-3">${price} /شخص</p>
                        <p class="text-muted mb-4">
                            سيتم توجيهك إلى صفحة التواصل لإتمام عملية الحجز
                        </p>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> هذا العرض محدود الوقت!
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <a href="index.html#contact" class="btn btn-primary">
                            <i class="bi bi-telephone me-2"></i>
                            متابعة الحجز
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('bookingModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('bookingModal'));
    modal.show();
    
    // Remove modal from DOM when hidden
    document.getElementById('bookingModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// ===== Urgency Messages =====
function showUrgencyMessage() {
    const messages = [
        'عدد المقاعد محدود - احجز الآن!',
        'آخر 3 مقاعد متاحة بهذا السعر!',
        'العرض ينتهي قريباً - لا تفوت الفرصة!',
        'حجز 5 أشخاص هذا العرض في آخر ساعة!'
    ];
    
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    showNotification(randomMessage, 'warning');
}

// ===== Analytics Tracking =====
function trackDealClick(dealTitle) {
    console.log(`Deal clicked: ${dealTitle}`);
    // In real app: send to analytics service
}

function trackOfferBooking(offerTitle, price) {
    console.log(`Booking attempt: ${offerTitle} - ${price}`);
    // In real app: send to analytics service
}

// ===== Price Calculator =====
function calculateGroupDiscount(basePrice, numberOfPeople) {
    let discount = 0;
    
    if (numberOfPeople >= 4) {
        discount = 0.1; // 10% discount for 4+ people
    } else if (numberOfPeople >= 6) {
        discount = 0.15; // 15% discount for 6+ people
    } else if (numberOfPeople >= 8) {
        discount = 0.2; // 20% discount for 8+ people
    }
    
    const discountedPrice = basePrice * (1 - discount);
    return {
        originalPrice: basePrice,
        discountedPrice: discountedPrice,
        savings: basePrice - discountedPrice,
        discountPercentage: discount * 100
    };
}

// ===== Offer Comparison =====
function initOfferComparison() {
    const compareButtons = document.querySelectorAll('.compare-offer');
    let selectedOffers = [];
    
    compareButtons.forEach(button => {
        button.addEventListener('click', function() {
            const offerCard = this.closest('.offer-card');
            const offerData = extractOfferData(offerCard);
            
            if (selectedOffers.length < 3) {
                selectedOffers.push(offerData);
                this.classList.add('selected');
                this.innerHTML = '<i class="bi bi-check me-2"></i>مُحدد للمقارنة';
            }
            
            if (selectedOffers.length >= 2) {
                showComparisonModal(selectedOffers);
            }
        });
    });
}

function extractOfferData(offerCard) {
    return {
        title: offerCard.querySelector('.card-title, h3').textContent,
        price: offerCard.querySelector('.new-price').textContent,
        originalPrice: offerCard.querySelector('.old-price')?.textContent || '',
        image: offerCard.querySelector('img').src,
        features: Array.from(offerCard.querySelectorAll('.list-unstyled li')).map(li => li.textContent)
    };
}

// ===== Utility Functions =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="bi bi-exclamation-triangle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// ===== CSS Animations =====
const style = document.createElement('style');
style.textContent = `
    .pulse-deal {
        animation: pulse-glow 2s infinite;
    }
    
    @keyframes pulse-glow {
        0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
        50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
        100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
    }
    
    .urgent {
        animation: urgent-blink 1s infinite;
    }
    
    @keyframes urgent-blink {
        0%, 50% { background-color: #dc3545; }
        51%, 100% { background-color: #ff6b6b; }
    }
    
    .expired {
        background-color: #6c757d !important;
        opacity: 0.7;
    }
`;
document.head.appendChild(style);
