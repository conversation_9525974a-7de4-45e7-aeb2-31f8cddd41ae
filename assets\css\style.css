/* ===== CSS Variables ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --info-color: #0891b2;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    
    --font-family-primary: 'Cairo', 'IBM Plex Sans Arabic', sans-serif;
    --font-family-secondary: 'IBM Plex Sans Arabic', 'Cairo', sans-serif;
    
    --border-radius: 0.5rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    line-height: 1.6;
    color: var(--dark-color);
    overflow-x: hidden;
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="ltr"] {
    text-align: left;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-primary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.display-4 {
    font-weight: 700;
}

.lead {
    font-size: 1.25rem;
    font-weight: 300;
}

/* ===== Navigation ===== */
.navbar {
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.95) !important;
    box-shadow: var(--box-shadow);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color) !important;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background-color: var(--primary-color);
}

/* ===== Hero Section ===== */
.hero-section {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-image:
        url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'),
        linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
    background-size: cover;
    background-position: center;
    background-blend-mode: overlay;
    z-index: 0;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-badge {
    animation: pulse 2s infinite;
}

.hero-title {
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
    font-size: 4rem;
    line-height: 1.1;
}

.hero-subtitle {
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.5);
    max-width: 700px;
    margin: 0 auto 2rem;
    font-size: 1.3rem;
    font-weight: 300;
}

.hero-btn {
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.hero-stats {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item h3 {
    font-size: 2.5rem;
    color: #fff;
}

.stat-item small {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.hero-buttons .btn {
    margin: 0.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: var(--transition);
}

.hero-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-indicator a {
    font-size: 2rem;
    animation: bounce 2s infinite;
    text-decoration: none;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* ===== Feature Icons ===== */
.feature-icon {
    width: 90px;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.2rem;
    transition: var(--transition);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
    opacity: 0;
    transition: var(--transition);
}

.feature-icon:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.feature-icon:hover::before {
    opacity: 1;
}

/* Feature Section Enhancement */
#features .container > .row > .col-lg-3 {
    margin-bottom: 2rem;
}

#features h5 {
    margin-top: 1rem;
    font-size: 1.3rem;
    color: var(--dark-color);
}

#features p {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--secondary-color);
}

/* ===== Animations ===== */
.animate-fade-in {
    animation: fadeIn 1s ease-out;
}

.animate-fade-in-delay {
    animation: fadeIn 1s ease-out 0.3s both;
}

.animate-fade-in-delay-2 {
    animation: fadeIn 1s ease-out 0.6s both;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section .lead {
        font-size: 1.1rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* ===== Accessibility ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
.btn:focus,
.nav-link:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== Custom Bootstrap Overrides ===== */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* ===== Language Toggle ===== */
#languageToggle {
    border-radius: 20px;
    font-size: 0.875rem;
}

/* ===== Smooth Scrolling ===== */
html {
    scroll-behavior: smooth;
}

/* ===== Section Spacing ===== */
section {
    padding: 4rem 0;
}

@media (max-width: 768px) {
    section {
        padding: 2rem 0;
    }
}

/* ===== Destination Cards ===== */
.destination-card {
    transition: var(--transition);
    overflow: hidden;
    border-radius: 15px;
    height: 100%;
    min-height: 500px;
}

.destination-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.destination-card .card-img-wrapper {
    position: relative;
    height: 280px;
    overflow: hidden;
}

.destination-card .card-img-top {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.destination-card:hover .card-img-top {
    transform: scale(1.1);
}

.destination-card .card-overlay {
    background: linear-gradient(transparent 50%, rgba(0, 0, 0, 0.8));
    opacity: 0;
    transition: var(--transition);
}

.destination-card:hover .card-overlay {
    opacity: 1;
}

.destination-card .card-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;
}

.destination-card .card-title {
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
    color: var(--dark-color);
}

.destination-card .card-text {
    flex-grow: 1;
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* ===== Service Cards ===== */
.service-card {
    transition: var(--transition);
    border: 1px solid transparent;
}

.service-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.service-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    transition: var(--transition);
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

/* ===== Testimonial Cards ===== */
.testimonial-card {
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.testimonial-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* ===== Contact Section ===== */
.contact-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-info a {
    color: var(--secondary-color);
    transition: var(--transition);
}

.contact-info a:hover {
    color: var(--primary-color);
}

/* ===== Form Styles ===== */
.form-control,
.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* ===== Footer ===== */
footer {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.footer-section h5,
.footer-section h6 {
    color: #ffffff;
}

.footer-links a:hover,
.social-links a:hover {
    color: var(--primary-color) !important;
    transform: translateY(-2px);
}

.newsletter-form .input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.newsletter-form .form-control {
    border: none;
    border-radius: 0;
}

.newsletter-form .btn {
    border-radius: 0;
}

/* ===== Back to Top Button ===== */
#backToTop {
    width: 50px;
    height: 50px;
    border-radius: 50% !important;
    transition: var(--transition);
}

#backToTop:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* ===== Additional Animations ===== */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

/* ===== Responsive Enhancements ===== */
@media (max-width: 992px) {
    .destination-card,
    .service-card,
    .testimonial-card {
        margin-bottom: 2rem;
    }
}

/* ===== Page Header Styles ===== */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    margin-top: 76px;
}

.page-header .breadcrumb-item a {
    text-decoration: none;
}

.page-header .breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.7);
}

/* ===== Offers Page Styles ===== */
.offer-card {
    transition: var(--transition);
    border-radius: 15px;
    overflow: hidden;
}

.offer-card .card-img {
    height: 300px;
    object-fit: cover;
    transition: var(--transition);
}

.offer-card:hover .card-img {
    transform: scale(1.05);
}

.flash-deal {
    position: relative;
    height: 400px;
}

.flash-deal .card-img-overlay {
    background: linear-gradient(transparent 40%, rgba(0, 0, 0, 0.8));
}

.offer-content {
    z-index: 2;
}

.old-price {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

.new-price {
    color: #ffc107;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.countdown-timer {
    z-index: 3;
}

.timer-display {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--success-color) 100%);
}

/* ===== Enhanced Animations ===== */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-fade-in-delay-3 {
    animation: fadeIn 1s ease-out 0.9s both;
}

.animate-fade-in-delay-4 {
    animation: fadeIn 1s ease-out 1.2s both;
}

/* ===== Responsive Enhancements ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        padding: 1rem;
        margin-top: 2rem;
    }

    .stat-item h3 {
        font-size: 2rem;
    }

    .display-5 {
        font-size: 2rem;
    }

    .service-icon,
    .contact-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .footer-section {
        margin-bottom: 2rem;
    }

    .social-links a {
        margin-bottom: 0.5rem;
    }

    .flash-deal {
        height: 350px;
        margin-bottom: 2rem;
    }

    .offer-content h3 {
        font-size: 1.5rem;
    }

    .countdown-timer {
        position: relative !important;
        margin: 1rem 0;
    }
}
