/* ===== Authentication Page Styles ===== */

.auth-page {
    background: #f8fafc;
    font-family: var(--font-family-primary);
}

/* ===== Auth Section ===== */
.auth-section {
    min-height: 100vh;
    position: relative;
    padding-top: 80px;
}

.auth-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-image: 
        url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'),
        linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    background-size: cover;
    background-position: center;
    background-blend-mode: overlay;
    z-index: 0;
}

.auth-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.auth-container {
    position: relative;
    z-index: 2;
    max-width: 1000px;
    margin: 0 auto;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===== Welcome Side ===== */
.auth-welcome {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    position: relative;
    overflow: hidden;
}

.auth-welcome::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.welcome-content {
    position: relative;
    z-index: 2;
    min-height: 500px;
}

.welcome-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.welcome-features .feature-item {
    font-size: 1.1rem;
    opacity: 0.9;
    transition: all 0.3s ease;
}

.welcome-features .feature-item:hover {
    opacity: 1;
    transform: translateX(-5px);
}

/* ===== Forms Side ===== */
.auth-forms {
    background: white;
    position: relative;
}

.forms-container {
    min-height: 500px;
    display: flex;
    align-items: center;
}

.auth-form {
    width: 100%;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Form Elements ===== */
.form-control {
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    transform: translateY(-1px);
}

.input-group-text {
    border: 2px solid #e2e8f0;
    border-right: none;
    border-radius: 10px 0 0 10px;
    background: #f8fafc;
    color: var(--secondary-color);
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 10px 10px 0;
}

.input-group .btn {
    border: 2px solid #e2e8f0;
    border-left: none;
    border-radius: 0 10px 10px 0;
}

/* ===== Buttons ===== */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    border: none;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

.btn-outline-secondary,
.btn-outline-danger,
.btn-outline-primary,
.btn-outline-info {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* ===== Social Login ===== */
.social-login .btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-login .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* ===== Password Strength ===== */
.password-strength .progress {
    border-radius: 10px;
    overflow: hidden;
}

.password-strength .progress-bar {
    transition: all 0.3s ease;
}

.password-weak { background-color: #dc3545; }
.password-medium { background-color: #ffc107; }
.password-strong { background-color: #28a745; }

/* ===== Form Validation ===== */
.was-validated .form-control:valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l-.94-.94L4.5 8.5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* ===== Loading States ===== */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ===== Responsive Design ===== */
@media (max-width: 992px) {
    .auth-welcome {
        display: none;
    }
    
    .auth-container {
        margin: 2rem;
    }
    
    .forms-container {
        padding: 2rem !important;
    }
}

@media (max-width: 768px) {
    .auth-container {
        margin: 1rem;
    }
    
    .forms-container {
        padding: 1.5rem !important;
    }
    
    .welcome-content {
        min-height: 300px;
        padding: 2rem !important;
    }
    
    .social-login .btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}

/* ===== Animation Delays ===== */
.auth-form .mb-3:nth-child(1) { animation-delay: 0.1s; }
.auth-form .mb-3:nth-child(2) { animation-delay: 0.2s; }
.auth-form .mb-3:nth-child(3) { animation-delay: 0.3s; }
.auth-form .mb-3:nth-child(4) { animation-delay: 0.4s; }
.auth-form .mb-3:nth-child(5) { animation-delay: 0.5s; }

/* ===== Custom Scrollbar ===== */
.auth-forms::-webkit-scrollbar {
    width: 6px;
}

.auth-forms::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.auth-forms::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

.auth-forms::-webkit-scrollbar-thumb:hover {
    background: #1d4ed8;
}
